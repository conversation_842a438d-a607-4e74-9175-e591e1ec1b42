// 实验数据解析服务
// 根据不同的实验类型解析JSON数据

// 制流电路实验数据接口
export interface CurrentControlCircuitData {
  student_id: string
  student_name: string
  k1Data: Array<{
    ratio: number
    current: number | null
  }>
  k01Data: Array<{
    ratio: number
    current: number | null
  }>
  temperature?: number
  humidity?: number
  notes?: string
}

// 示波器实验数据接口（示例）
export interface OscilloscopeData {
  student_id: string
  student_name: string
  frequency: number
  amplitude: number
  waveform_type: string
  measurements: Array<{
    time: number
    voltage: number
  }>
  notes?: string
}

// 通用实验记录接口
export interface ExperimentRecord {
  id: number
  student_id?: number
  experiment_type_id: number
  experiment_code: string
  experiment_name: string
  submission_data: string | object
  plot_data?: string
  analysis_result?: string
  is_passed?: boolean
  score?: number
  submitted_at: string
  status: string
}

// 解析后的实验数据
export interface ParsedExperimentData {
  type: string
  data: any
  isValid: boolean
  errors?: string[]
}

/**
 * 解析制流电路实验数据
 */
export function parseCurrentControlCircuitData(submissionData: string | object): ParsedExperimentData {
  try {
    let data: any
    
    if (typeof submissionData === 'string') {
      data = JSON.parse(submissionData)
    } else {
      data = submissionData
    }

    const errors: string[] = []
    
    // 验证必需字段
    if (!data.student_id) {
      errors.push('缺少学号信息')
    }
    
    if (!data.student_name) {
      errors.push('缺少学生姓名')
    }

    // 验证k1Data
    if (!Array.isArray(data.k1Data)) {
      errors.push('k=1电流数据格式错误')
    } else {
      data.k1Data.forEach((item: any, index: number) => {
        if (typeof item.ratio !== 'number') {
          errors.push(`k=1数据第${index + 1}行：接入比例格式错误`)
        }
        if (item.current !== null && typeof item.current !== 'number') {
          errors.push(`k=1数据第${index + 1}行：电流值格式错误`)
        }
      })
    }

    // 验证k01Data
    if (!Array.isArray(data.k01Data)) {
      errors.push('k=0.1电流数据格式错误')
    } else {
      data.k01Data.forEach((item: any, index: number) => {
        if (typeof item.ratio !== 'number') {
          errors.push(`k=0.1数据第${index + 1}行：接入比例格式错误`)
        }
        if (item.current !== null && typeof item.current !== 'number') {
          errors.push(`k=0.1数据第${index + 1}行：电流值格式错误`)
        }
      })
    }

    const parsedData: CurrentControlCircuitData = {
      student_id: data.student_id || '',
      student_name: data.student_name || '',
      k1Data: data.k1Data || [],
      k01Data: data.k01Data || [],
      temperature: data.temperature,
      humidity: data.humidity,
      notes: data.notes
    }

    return {
      type: 'current_control_circuit',
      data: parsedData,
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  } catch (error) {
    return {
      type: 'current_control_circuit',
      data: null,
      isValid: false,
      errors: ['JSON数据解析失败: ' + (error as Error).message]
    }
  }
}

/**
 * 解析示波器实验数据（示例）
 */
export function parseOscilloscopeData(submissionData: string | object): ParsedExperimentData {
  try {
    let data: any
    
    if (typeof submissionData === 'string') {
      data = JSON.parse(submissionData)
    } else {
      data = submissionData
    }

    const parsedData: OscilloscopeData = {
      student_id: data.student_id || '',
      student_name: data.student_name || '',
      frequency: data.frequency || 0,
      amplitude: data.amplitude || 0,
      waveform_type: data.waveform_type || '',
      measurements: data.measurements || [],
      notes: data.notes
    }

    return {
      type: 'oscilloscope',
      data: parsedData,
      isValid: true
    }
  } catch (error) {
    return {
      type: 'oscilloscope',
      data: null,
      isValid: false,
      errors: ['JSON数据解析失败: ' + (error as Error).message]
    }
  }
}

/**
 * 根据实验类型解析数据
 */
export function parseExperimentData(experimentCode: string, submissionData: string | object): ParsedExperimentData {
  switch (experimentCode) {
    case 'current_control_circuit':
      return parseCurrentControlCircuitData(submissionData)
    case 'oscilloscope':
      return parseOscilloscopeData(submissionData)
    default:
      return {
        type: 'unknown',
        data: submissionData,
        isValid: false,
        errors: [`未知的实验类型: ${experimentCode}`]
      }
  }
}

/**
 * 计算制流电路实验的统计信息
 */
export function calculateCurrentControlCircuitStats(data: CurrentControlCircuitData) {
  const stats = {
    k1Stats: calculateDataStats(data.k1Data),
    k01Stats: calculateDataStats(data.k01Data)
  }
  
  return stats
}

/**
 * 计算数据统计信息
 */
function calculateDataStats(dataArray: Array<{ ratio: number; current: number | null }>) {
  const validData = dataArray.filter(item => item.current !== null).map(item => item.current as number)
  
  if (validData.length === 0) {
    return {
      count: 0,
      min: null,
      max: null,
      average: null,
      standardDeviation: null
    }
  }
  
  const min = Math.min(...validData)
  const max = Math.max(...validData)
  const average = validData.reduce((sum, val) => sum + val, 0) / validData.length
  
  // 计算标准差
  const variance = validData.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / validData.length
  const standardDeviation = Math.sqrt(variance)
  
  return {
    count: validData.length,
    min: Number(min.toFixed(4)),
    max: Number(max.toFixed(4)),
    average: Number(average.toFixed(4)),
    standardDeviation: Number(standardDeviation.toFixed(4))
  }
}
